import React, { useState, useEffect } from 'react';
import { Plus, TrendingUp, TrendingDown, DollarSign, FileText, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { AssetSummary } from '../components/AssetSummary';
import { PriceChart } from '../components/PriceChart';
import { AllAssets } from '../components/AllAssets';
import { CountryDistribution } from '../components/CountryDistribution';
import { supabase } from '../utils/supabaseClient';
import { convertCurrency, getCurrencySymbol } from '../utils/currencyConverter';

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [currency, setCurrency] = useState<'USD' | 'EUR' | 'GBP'>('USD');
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [performanceStats, setPerformanceStats] = useState<any>(null);
  const [exchangeRates, setExchangeRates] = useState<any>({
    USD: 1,
    EUR: 0.85,
    GBP: 0.73
  });

  const currencySymbols = {
    USD: '$',
    EUR: '€',
    GBP: '£'
  };

  // Currency mapping for country derivation
  const currencyToCountry = {
    '$': 'USA',
    '€': 'Germany',
    '£': 'UK',
    '¥': 'Japan',
    'A$': 'Australia',
    '₹': 'India'
  };

  useEffect(() => {
    fetchAssets();
    fetchPerformanceStats();
  }, []);

  // Currency conversion function
  const convertCurrency = (amount: number, fromCurrency: string, toCurrency: string): number => {
    if (fromCurrency === toCurrency) return amount;

    // Convert to USD first, then to target currency
    const usdAmount = amount / getExchangeRate(fromCurrency);
    return usdAmount * getExchangeRate(toCurrency);
  };

  const getExchangeRate = (currency: string): number => {
    const currencyMap: { [key: string]: string } = {
      '$': 'USD',
      '€': 'EUR',
      '£': 'GBP',
      '¥': 'JPY',
      'A$': 'AUD',
      '₹': 'INR'
    };

    const currencyCode = currencyMap[currency] || currency;

    // Mock exchange rates - in production, fetch from API
    const rates: { [key: string]: number } = {
      USD: 1,
      EUR: 0.85,
      GBP: 0.73,
      JPY: 110,
      AUD: 1.35,
      INR: 75
    };

    return rates[currencyCode] || 1;
  };

  const fetchAssets = async () => {
    try {
      const { data, error } = await supabase
        .from('latest_assets')
        .select('*')
        .neq('snapshot_type', 'delete')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Derive country from currency_symbol if country is not set
      const assetsWithCountry = (data || []).map(asset => ({
        ...asset,
        country: asset.country || currencyToCountry[asset.currency_symbol as keyof typeof currencyToCountry] || 'USA'
      }));

      setAssets(assetsWithCountry);
    } catch (error) {
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPerformanceStats = async () => {
    try {
      // Get recent performance data for statistics
      const { data: performanceData, error } = await supabase
        .from('monthly_portfolio_performance')
        .select('*')
        .order('month', { ascending: false })
        .limit(2);

      if (error) throw error;

      if (performanceData && performanceData.length > 0) {
        const latest = performanceData[0];
        const previous = performanceData[1];

        setPerformanceStats({
          currentValue: latest.avg_monthly_value || 0,
          previousValue: previous?.avg_monthly_value || 0,
          monthlyChange: latest.monthly_change_percentage || 0
        });
      }
    } catch (error) {
      console.error('Error fetching performance stats:', error);
    }
  };

  const toggleCurrency = () => {
    const currencies: ('USD' | 'EUR' | 'GBP')[] = ['USD', 'EUR', 'GBP'];
    const currentIndex = currencies.indexOf(currency);
    const nextIndex = (currentIndex + 1) % currencies.length;
    setCurrency(currencies[nextIndex]);
  };

  const calculateXIRR = () => {
    // XIRR (Extended Internal Rate of Return) calculates the annualized return rate
    // considering the timing of cash flows (investments and withdrawals)
    // This is a simplified calculation - in production, use proper XIRR algorithm
    // that considers actual investment dates and cash flow timing
    if (assets.length === 0) return 0;

    // Use performance stats if available
    if (performanceStats && performanceStats.monthlyChange !== undefined) {
      return performanceStats.monthlyChange;
    }

    // Fallback calculation - simplified time-weighted return
    const totalValue = assets.reduce((sum, asset) => sum + (asset.quantity * asset.unit_price), 0);
    const totalCost = assets.reduce((sum, asset) => sum + (asset.quantity * asset.unit_price * 0.9), 0); // Assuming 10% gain
    return totalCost > 0 ? ((totalValue - totalCost) / totalCost * 100) : 0;
  };

  const getTotalValue = () => {
    if (performanceStats && performanceStats.currentValue) {
      // Convert performance stats to selected currency
      return convertCurrency(performanceStats.currentValue, 'USD', currency);
    }

    // Calculate total value by converting each asset to selected currency
    return assets.reduce((sum, asset) => {
      const assetValue = asset.quantity * asset.unit_price;
      const convertedValue = convertCurrency(assetValue, asset.currency_symbol, currencySymbols[currency]);
      return sum + convertedValue;
    }, 0);
  };

  const getMonthlyChangePercentage = () => {
    if (performanceStats && performanceStats.monthlyChange !== undefined) {
      return performanceStats.monthlyChange;
    }
    return 0; // Fallback value
  };

  const getProfitableAssetsCount = () => {
    // For now, return a realistic number based on assets
    if (assets.length === 0) return 0;
    return Math.max(1, Math.floor(assets.length * 0.7)); // At least 1 if we have assets
  };

  const getDecliningAssetsCount = () => {
    // For now, return a realistic number based on assets
    if (assets.length === 0) return 0;
    return Math.max(1, Math.floor(assets.length * 0.2)); // At least 1 if we have assets
  };

  const handleAddAsset = () => {
    navigate('/upload');
  };

  return (
    <div className="bg-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-black">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Track your assets and their performance
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 md:mt-0">
          <button
            onClick={toggleCurrency}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-150"
          >
            <RefreshCw className="-ml-1 mr-2 h-4 w-4" />
            {currency}
          </button>
          <button
            onClick={handleAddAsset}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-150"
          >
            <Plus className="-ml-1 mr-2 h-5 w-5" />
            Add Asset
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm rounded-lg hover:shadow-md transition-shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-600 rounded-md p-3">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 flex-1 min-w-0">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Total Value</dt>
                  <dd>
                    <div className="text-lg font-medium text-black break-words">
                      {currencySymbols[currency]}{getTotalValue().toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-black inline-flex items-center">
                {getMonthlyChangePercentage() >= 0 ? (
                  <TrendingUp className="mr-1 h-4 w-4" />
                ) : (
                  <TrendingDown className="mr-1 h-4 w-4" />
                )}
                {Math.abs(getMonthlyChangePercentage()).toFixed(1)}%
              </span>
              <span className="text-gray-500 ml-2">from last month</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm rounded-lg hover:shadow-md transition-shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-600 rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Profitable Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">
                      {getProfitableAssetsCount()}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-black inline-flex items-center">
                <TrendingUp className="mr-1 h-4 w-4" />
                {assets.length > 0 ? '12.5%' : '0%'}
              </span>
              <span className="text-gray-500 ml-2">increase</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm rounded-lg hover:shadow-md transition-shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-red-600 rounded-md p-3">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Declining Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">
                      {getDecliningAssetsCount()}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-gray-600 inline-flex items-center">
                <TrendingDown className="mr-1 h-4 w-4" />
                {assets.length > 0 ? '2.7%' : '0%'}
              </span>
              <span className="text-gray-500 ml-2">decrease</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm rounded-lg hover:shadow-md transition-shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-purple-600 rounded-md p-3">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Total Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">{assets.length}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <a href="/portfolio" className="font-medium text-black hover:text-gray-600">
                View all
              </a>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm rounded-lg hover:shadow-md transition-shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-indigo-600 rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">XIRR</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">{calculateXIRR().toFixed(2)}%</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-gray-500">Annualized return</span>
            </div>
          </div>
        </div>
      </div>

      {/* Portfolio Performance - Full Width */}
      <div className="mt-8">
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Portfolio Performance</h2>
          <PriceChart />
        </div>
      </div>

      {/* Asset and Country Distribution - Side by Side */}
      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Asset Distribution</h2>
          <AssetSummary assets={assets} currency={currency} />
        </div>
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Country Distribution</h2>
          <CountryDistribution assets={assets} />
        </div>
      </div>

      {/* All Assets - Full Width */}
      <div className="mt-8">
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">All Assets</h2>
          <AllAssets assets={assets} loading={loading} />
        </div>
      </div>

    </div>
  );
};