// Currency conversion utilities
// In production, you would fetch real-time rates from an API like exchangerate-api.com

export interface ExchangeRates {
  [key: string]: number;
}

// Mock exchange rates - in production, fetch from API
const MOCK_EXCHANGE_RATES: ExchangeRates = {
  USD: 1,
  EUR: 0.85,
  GBP: 0.73,
  JPY: 110,
  AUD: 1.35,
  INR: 75,
  CAD: 1.25,
  CHF: 0.92,
  CNY: 6.45
};

// Currency symbol to currency code mapping
const CURRENCY_SYMBOL_TO_CODE: { [key: string]: string } = {
  '$': 'USD',
  '€': 'EUR', 
  '£': 'GBP',
  '¥': 'JPY',
  'A$': 'AUD',
  '₹': 'INR',
  'C$': 'CAD',
  'CHF': 'CHF',
  '¥': 'CNY'
};

// Currency code to symbol mapping
const CURRENCY_CODE_TO_SYMBOL: { [key: string]: string } = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  AUD: 'A$',
  INR: '₹',
  CAD: 'C$',
  CHF: 'CHF',
  CNY: '¥'
};

/**
 * Convert amount from one currency to another
 * @param amount - Amount to convert
 * @param fromCurrency - Source currency (symbol or code)
 * @param toCurrency - Target currency (symbol or code)
 * @returns Converted amount
 */
export const convertCurrency = (amount: number, fromCurrency: string, toCurrency: string): number => {
  if (fromCurrency === toCurrency) return amount;
  
  // Convert symbols to currency codes
  const fromCode = CURRENCY_SYMBOL_TO_CODE[fromCurrency] || fromCurrency;
  const toCode = CURRENCY_SYMBOL_TO_CODE[toCurrency] || toCurrency;
  
  // Get exchange rates
  const fromRate = MOCK_EXCHANGE_RATES[fromCode] || 1;
  const toRate = MOCK_EXCHANGE_RATES[toCode] || 1;
  
  // Convert to USD first, then to target currency
  const usdAmount = amount / fromRate;
  return usdAmount * toRate;
};

/**
 * Get exchange rate for a currency
 * @param currency - Currency symbol or code
 * @returns Exchange rate relative to USD
 */
export const getExchangeRate = (currency: string): number => {
  const currencyCode = CURRENCY_SYMBOL_TO_CODE[currency] || currency;
  return MOCK_EXCHANGE_RATES[currencyCode] || 1;
};

/**
 * Get currency symbol from currency code
 * @param currencyCode - Currency code (e.g., 'USD')
 * @returns Currency symbol (e.g., '$')
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  return CURRENCY_CODE_TO_SYMBOL[currencyCode] || currencyCode;
};

/**
 * Get currency code from currency symbol
 * @param currencySymbol - Currency symbol (e.g., '$')
 * @returns Currency code (e.g., 'USD')
 */
export const getCurrencyCode = (currencySymbol: string): string => {
  return CURRENCY_SYMBOL_TO_CODE[currencySymbol] || currencySymbol;
};

/**
 * Format currency amount with proper symbol and locale
 * @param amount - Amount to format
 * @param currency - Currency symbol or code
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string, locale: string = 'en-US'): string => {
  const symbol = CURRENCY_CODE_TO_SYMBOL[currency] || currency;
  return `${symbol}${amount.toLocaleString(locale, { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })}`;
};

/**
 * Fetch real-time exchange rates (placeholder for production implementation)
 * In production, you would call a real API like:
 * - https://api.exchangerate-api.com/v4/latest/USD
 * - https://openexchangerates.org/api/latest.json
 * - https://api.fixer.io/latest
 */
export const fetchExchangeRates = async (): Promise<ExchangeRates> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // In production, replace with actual API call:
  // const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
  // const data = await response.json();
  // return data.rates;
  
  return MOCK_EXCHANGE_RATES;
};

/**
 * Country to currency mapping for automatic currency detection
 */
export const COUNTRY_TO_CURRENCY: { [key: string]: string } = {
  'USA': 'USD',
  'United States': 'USD',
  'Germany': 'EUR',
  'France': 'EUR',
  'Spain': 'EUR',
  'Italy': 'EUR',
  'Netherlands': 'EUR',
  'UK': 'GBP',
  'United Kingdom': 'GBP',
  'Britain': 'GBP',
  'Japan': 'JPY',
  'Australia': 'AUD',
  'India': 'INR',
  'Canada': 'CAD',
  'Switzerland': 'CHF',
  'China': 'CNY',
  'Global': 'USD' // Default for global assets
};

/**
 * Get currency from country name
 * @param country - Country name
 * @returns Currency code
 */
export const getCurrencyFromCountry = (country: string): string => {
  return COUNTRY_TO_CURRENCY[country] || 'USD';
};
